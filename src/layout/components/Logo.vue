<template>
  <section class="system-logo" :class="{ collapsed: props.collapsed }" @click="toHome">
    <!-- <img v-if="logo" class="logo" :src="logo" alt="logo" style="width: 200px;" /> -->
    <!-- <img v-else class="logo" src="/logo.svg" alt="logo" /> -->
    <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-start; width: 100%;height: 100%;">
      <img class="logo" src="/src/assets/images/jc-logo.png" alt="logo" />
      <div style="display: flex;flex-direction: column;align-items: center;justify-content: center;height: 80%;">
       <div class="system-name gi_line_1 grand_text" >
        <!-- {{ title.slice(0,3) }}-{{ title.slice(3,6) }} -->
         SEE-NOW
       </div>
        <!-- <span ></span> -->
        <span class="system-name gi_line_1 gran-slogn">聚力创造 引领增长</span>
      </div>
    </div>

  </section>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
})
const appStore = useAppStore()
const title = computed(() => appStore.getTitle())
const logo = computed(() => appStore.getLogo())

interface Props {
  collapsed?: boolean
}
const router = useRouter()
// 跳转首页
const toHome = () => {
  router.push('/')
}
</script>

<style scoped lang="scss">
.system-logo {
  height: 56px;
  padding: 0 12px;
  color: var(--color-text-1);
  font-size: 20px;
  line-height: 1;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  cursor: pointer;
  user-select: none;
  box-sizing: border-box;

  &.collapsed {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .system-name {
      display: none;
    }
  }
  .grand_text{
      font-size: 20px;
      font-weight: bold;
      background: linear-gradient(to bottom, #442C5B, #45BFD5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
      letter-spacing: 3px;
  }
  .gran-slogn{
    font-size: 10px;
    letter-spacing: 1px;
    font-weight: bold;
    background: linear-gradient(to top, #442C5B, #45BFD5);
    -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
  }
  .logo {
    margin-right: 5px;
    margin-top: 5px;
    width: 40px;
    height: 40px;
    border-radius: 6px;
    transition: all 0.2s;
    overflow: hidden;
    flex-shrink: 0;
  }

  .system-name {
    padding-left: 6px;
    white-space: nowrap;
    transition: color 0.3s;
    line-height: 1.5;
    display: inline-flex;
    align-items: center;

    &:hover {
      color: $color-theme !important;
      cursor: pointer;
    }
  }
}
</style>
